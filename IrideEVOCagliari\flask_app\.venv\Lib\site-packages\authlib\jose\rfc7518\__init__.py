from .ec_key import <PERSON><PERSON><PERSON>
from .jwe_algs import <PERSON><PERSON><PERSON>_AL<PERSON>_AL<PERSON><PERSON><PERSON><PERSON><PERSON>
from .jwe_algs import AESAlgorithm
from .jwe_algs import ECDHESAlgorithm
from .jwe_algs import u32be_len_input
from .jwe_encs import <PERSON><PERSON><PERSON>_<PERSON><PERSON>_AL<PERSON><PERSON><PERSON><PERSON><PERSON>
from .jwe_encs import CBCHS<PERSON><PERSON>ncAlgorithm
from .jwe_zips import <PERSON>flate<PERSON><PERSON><PERSON><PERSON>gorith<PERSON>
from .jws_algs import <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .oct_key import OctKey
from .rsa_key import <PERSON><PERSON><PERSON><PERSON>


def register_jws_rfc7518(cls):
    for algorithm in JWS_ALGORITHMS:
        cls.register_algorithm(algorithm)


def register_jwe_rfc7518(cls):
    for algorithm in JWE_ALG_ALGORITHMS:
        cls.register_algorithm(algorithm)

    for algorithm in JWE_ENC_ALGORITHMS:
        cls.register_algorithm(algorithm)

    cls.register_algorithm(DeflateZipAlgorithm())


__all__ = [
    "register_jws_rfc7518",
    "register_jwe_rfc7518",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "u32be_len_input",
    "AESAlgorithm",
    "ECDHESAlgorithm",
    "CBCHS2EncAlgorithm",
]
