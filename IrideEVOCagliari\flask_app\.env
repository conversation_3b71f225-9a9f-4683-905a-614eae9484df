# Chiave segreta per la sessione Flask. Mantenerla costante.
# Puoi generarne una con: python -c 'import secrets; print(secrets.token_hex(16))'
FLASK_SECRET_KEY='una-chiave-segreta-casuale-e-robusta'

# Credenziali OIDC fornite da WSO2
WSO2_CLIENT_ID='fdwHDnf7jH1ZV8Nl1hzEcug9Mnca'
WSO2_CLIENT_SECRET='KQ0pDcVRpPUGiQF7_VlzhqIKyyAa'

# URL di discovery di WSO2. Contiene tutti gli endpoint necessari (authorize, token, userinfo, etc.)
# Sostituisci iam.comune.cagliari.it con il tuo host corretto se diverso.
WSO2_DISCOVERY_URL='https://iam.comune.cagliari.it/oauth2/token/.well-known/openid-configuration'
