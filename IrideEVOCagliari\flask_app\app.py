import os
from flask import Flask, redirect, url_for, session, jsonify, abort
from authlib.integrations.flask_client import OAuth
from dotenv import load_dotenv

# Carica le variabili d'ambiente dal file .env
load_dotenv()

app = Flask(__name__)
# Usa una chiave segreta stabile per la sessione, caricata dall'ambiente
app.secret_key = os.getenv("FLASK_SECRET_KEY")

# Inizializza Authlib
oauth = OAuth(app)

# Registra il provider OIDC (WSO2)
# Authlib userà l'URL di discovery per trovare automaticamente
# gli endpoint di autorizzazione, token e userinfo.
oauth.register(
    name='wso2',
    client_id=os.getenv("WSO2_CLIENT_ID"),
    client_secret=os.getenv("WSO2_CLIENT_SECRET"),
    server_metadata_url=os.getenv("WSO2_DISCOVERY_URL"),
    client_kwargs={
        'scope': 'openid profile email'
    }
)

@app.route('/')
def index():
    """Pagina principale. Mostra lo stato dell'utente e link per login/logout."""
    user = session.get('user')
    if user:
        return f'Ciao, {user["name"]}! <a href="/logout">Logout</a> <br><a href="/profile">Vedi profilo</a>'
    return 'Benvenuto! <a href="/login">Login</a>'

@app.route('/login')
def login():
    """Redireziona l'utente a WSO2 per l'autenticazione."""
    # Ottiene il client registrato in modo sicuro e valida che non sia None
    client = oauth.create_client('wso2')
    if client is None:
        # Probabile problema di registrazione del provider o variabili d'ambiente mancanti
        abort(500, description="OIDC client 'wso2' non disponibile: verifica registrazione e variabili d'ambiente.")
    redirect_uri = url_for('authorize', _external=True)
    return client.authorize_redirect(redirect_uri)

@app.route('/authorize')
def authorize():
    """Callback dopo l'autenticazione. Scambia il codice per il token e salva l'utente in sessione."""
    client = oauth.create_client('wso2')
    token = client.authorize_access_token()
    # Il profilo utente è solitamente nel claim 'userinfo' o direttamente nel token ID
    user_info = client.parse_id_token(token)
    session['user'] = user_info
    return redirect(url_for('index'))

@app.route('/logout')
def logout():
    """Pulisce la sessione locale."""
    session.pop('user', None)
    # Per un logout completo (Single Sign-Out), potresti dover reindirizzare all'endpoint di logout di WSO2
    # wso2_logout_url = oauth.wso2.server_metadata.get('end_session_endpoint')
    # if wso2_logout_url:
    #     return redirect(f'{wso2_logout_url}?post_logout_redirect_uri={url_for("index", _external=True)}')
    return redirect(url_for('index'))

@app.route('/profile')
def profile():
    """Rotta protetta che mostra le informazioni dell'utente loggato."""
    user = session.get('user')
    if not user:
        return redirect(url_for('login'))
    return jsonify(user)

if __name__ == '__main__':
    # debug=False in produzione!
    app.run(debug=True, port=5000)