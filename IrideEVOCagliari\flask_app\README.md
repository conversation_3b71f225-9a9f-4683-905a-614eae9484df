# Flask + WSO2 Identity Server – OpenID Connect (OAuth2)
Guida rapida per un’applicazione Python/Flask che autentica gli utenti tramite WSO2 IS (eventualmente federato con Azure AD).

---

## 0. Requisiti

- Python ≥ 3.8
- uv package manager
- WSO2 Identity Server ≥ 5.11 (o qualsiasi istanza accessibile via HTTPS)
- utente amministratore di WSO2 IS per creare Service Provider

---

## 1. Installa le dipendenze

```bash
uv add flask requests-oauthlib
```

## 2. Registra un Service Provider in WSO2 IS

1. Accedi alla **Console di WSO2 → Service Providers → Add**
2. Inserisci nome (es. flask-oidc-demo) → **Register**
3. **Inbound Authentication Configuration → OAuth/OpenID Connect Configuration → Configure**
4. Compila:
   - **Callback Url:** <http://localhost:5000/callback>
   - **Grant Types:** spunta solo Code (Authorization Code)
5. <PERSON>va → annota:
   - **Client ID**
   - **Client Secret**

## 3. Parametri di configurazione

Sostituisci i placeholder nel file `app.py` con i valori corretti.

| Variabile | Valore d’esempio |
| --- | --- |
| `WSO2_ISSUER_URL` | https://wso2-host:9443/oauth2/token |
| `WSO2_AUTH_URL` | https://wso2-host:9443/oauth2/authorize |
| `WSO2_TOKEN_URL` | https://wso2-host:9443/oauth2/token |
| `WSO2_USERINFO` | https://wso2-host:9443/oauth2/userinfo |
| `CLIENT_ID` | Client ID appena ricavato |
| `CLIENT_SECRET` | Client Secret appena ricavato |

## 5. Avvia l'applicazione

```bash
export FLASK_APP=app.py   # Windows: set FLASK_APP=app.py
flask run
```

Apri il browser su http://localhost:5000 → clicca **Login** → autenticati con WSO2 IS → tornerai su `/profile` con i dati dell'utente.

## 6. Sicurezza in produzione

- Rimuovi `OAUTHLIB_INSECURE_TRANSPORT` e `verify=False`
- Usa certificati TLS validi sia su WSO2 IS che sull’app Flask (HTTPS)
- Conserva i token in modo sicuro (es. sessioni server-side criptate)
- Aggiungi CSRF e controlli di stato più rigidi se necessario

## 7. Flusso completo (riassunto)

1. Utente → Flask `/login`
2. Flask → redirect a WSO2 IS `/authorize`
3. WSO2 IS → autentica (eventualmente con Azure AD)
4. WSO2 IS → redirect a Flask `/callback` (con `?code=`)
5. Flask scambia il code con token-set
6. Flask chiama `/userinfo` e mostra i dati utente
